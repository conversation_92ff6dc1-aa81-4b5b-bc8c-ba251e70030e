# 扫一扫功能摄像头默认选择修复方案

## 问题分析

通过对比历史版本（test-qrcode.vue）和当前版本（inspect-scan/index.vue、visit-scan/index.vue）的代码，发现了导致摄像头默认选择不一致的主要原因：

### 1. 摄像头选择策略差异
- **历史版本**：在 `initScanner()` 中有完整的后置摄像头查找逻辑，包括华为/荣耀设备的特殊处理
- **当前版本**：虽然有 `getBackCamera()` 方法，但在某些情况下会被强制策略覆盖

### 2. 初始化约束条件差异
- **历史版本**：使用 `{ exact: selectedDevice.deviceId }` 精确指定设备
- **当前版本**：使用了多层保障机制，但第一层的 `facingMode: "environment"` 可能与设备ID冲突

### 3. 降级策略差异
- **历史版本**：有完整的降级策略，包括 `tryDirectMediaAccess()` 方法
- **当前版本**：降级策略不够完善

## 解决方案

### 1. 改进摄像头选择逻辑

在 `inspect-scan/index.vue` 和 `visit-scan/index.vue` 中，将原有的简单摄像头选择逻辑替换为历史版本的完整逻辑：

```javascript
// 如果没有找到明确的后置摄像头，使用改进的策略
if (!backCamera) {
  console.log("[InspectScan] 未找到明确的后置摄像头，开始使用改进策略");
  
  if (this.devices.length > 1) {
    // 策略1：查找后置摄像头（使用历史版本的完整逻辑）
    const backCameraIndex = this.devices.findIndex((device) => {
      const label = (device.label || "").toLowerCase();

      // 华为和荣耀特有标识检测
      const isHuaweiOrHonorBack =
        (label.includes("huawei") ||
          label.includes("honor") ||
          label.includes("华为") ||
          label.includes("荣耀")) &&
        (label.includes("back") ||
          label.includes("rear") ||
          label.includes("后") ||
          label.includes("主") ||
          // 华为设备常用"camera 0"表示后置摄像头
          (label.includes("camera 0") && !label.includes("front")) ||
          // 华为和荣耀设备常见后缀数字识别：通常数字小的是后置主摄
          (/camera\s*[0-2]$/.test(label) && !label.includes("front")));

      // 通用识别逻辑
      const isGeneralBack =
        (label.includes("back") ||
          label.includes("rear") ||
          label.includes("environment") ||
          label.includes("后置") ||
          label.includes("外部") ||
          label.includes("主摄") ||
          label.includes("wide") ||
          label.includes("ultra")) &&
        !(
          label.includes("front") ||
          label.includes("selfie") ||
          label.includes("user") ||
          label.includes("前置") ||
          label.includes("自拍")
        );

      return isHuaweiOrHonorBack || isGeneralBack;
    });

    if (backCameraIndex !== -1) {
      selectedDevice = this.devices[backCameraIndex];
    } else {
      // 策略2：如果找不到明确的后置摄像头，尝试使用最后一个设备
      selectedDevice = this.devices[this.devices.length - 1];
    }
  }
}
```

### 2. 优化扫描初始化策略

将原有的多层保障机制替换为历史版本的策略：

```javascript
try {
  // 首先尝试使用精确的设备ID约束（历史版本的主要策略）
  const constraints = {
    video: {
      deviceId: { exact: selectedDevice.deviceId },
      width: { ideal: 1280 },
      height: { ideal: 720 },
      focusMode: "continuous", // 连续对焦
      exposureMode: "continuous", // 连续曝光
    },
  };

  this.scanControls = await this.codeReader.decodeFromVideoDevice(
    selectedDevice.deviceId,
    this.videoElement,
    (result, error) => {
      // 处理扫描结果
    },
    constraints
  );
} catch (primaryError) {
  // 降级策略1：使用facingMode约束
  try {
    const fallbackConstraints = {
      video: {
        facingMode: { exact: "environment" },
        width: { ideal: 1280 },
        height: { ideal: 720 },
      },
    };
    // 使用降级策略
  } catch (fallbackError) {
    // 最终策略：基本约束
  }
}
```

## 修改的文件

1. **pages/inspect-scan/index.vue**
   - 改进摄像头选择逻辑（第277-357行）
   - 优化扫描初始化策略（第358-472行）

2. **pages/visit-scan/index.vue**
   - 改进摄像头选择逻辑（第378-458行）
   - 优化扫描初始化策略（第478-618行）

## 预期效果

1. **一致的摄像头选择**：所有手机上都会优先选择后置摄像头
2. **更好的兼容性**：特别是华为/荣耀设备的摄像头识别
3. **稳定的初始化**：使用精确的设备ID约束，避免facingMode冲突
4. **完善的降级策略**：当主要策略失败时，有完整的备用方案

## 测试建议

1. 在不同品牌的手机上测试扫一扫功能
2. 特别关注华为/荣耀设备的表现
3. 验证前后摄像头切换功能是否正常
4. 确认扫描功能的稳定性和识别率

## 业务逻辑保持不变

本次修改只针对摄像头选择和初始化逻辑，所有业务逻辑（扫码验证、API调用、页面跳转等）保持完全不变。
