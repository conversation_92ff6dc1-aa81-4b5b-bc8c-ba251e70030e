<template>
  <div class="container">
    <div class="scan-box">
      <video
        ref="video"
        class="scan-video"
        autoplay
        muted
        playsinline
        :controls="false"
        webkit-playsinline="true"
        x5-video-player-type="h5-page"
      ></video>
      <canvas ref="canvas" class="scan-canvas"></canvas>
      <div class="scan-line"></div>
      <div class="tip-text">
        <div>
          将{{ scanMode === "qr" ? "二维码" : "条形码" }}放入框内进行扫描
        </div>
        <div class="barcode-tip" v-if="scanMode === 'barcode'">
          扫描条形码时请保持水平并靠近<br />
          <span class="mode-restriction">当前模式只能扫描条形码</span>
        </div>
        <div class="qrcode-tip" v-if="scanMode === 'qr'">
          <span class="mode-restriction">当前模式只能扫描二维码</span>
        </div>
      </div>
    </div>
    <div class="scan-border"></div>
    <div v-if="showSuccessEffect" class="success-effect">
      <div class="success-icon">
        <uni-icons type="checkmarkempty" size="60" color="#fff"></uni-icons>
      </div>
      <div class="success-text">扫码成功</div>
    </div>
    <view class="camera-toggle-btn" @click="toggleCamera">
      <text>切换摄像头</text>
    </view>
    <view class="flashlight-btn" @click="toggleFlashlight">
      <uni-icons
        :type="flashlightOn ? 'fire-filled' : 'fire'"
        size="24"
        color="#fff"
      ></uni-icons>
      <text>{{ flashlightOn ? "关闭闪光灯" : "开启闪光灯" }}</text>
    </view>

    <view class="scan-mode-container">
      <view
        class="scan-mode-btn"
        :class="{ active: scanMode === 'qr' }"
        @click="switchScanMode('qr')"
      >
        <text>扫二维码</text>
      </view>
      <view
        class="scan-mode-btn"
        :class="{ active: scanMode === 'barcode' }"
        @click="switchScanMode('barcode')"
      >
        <text>扫条形码</text>
      </view>
    </view>
  </div>
</template>

<script>
import {
  BrowserMultiFormatReader,
  BarcodeFormat,
  DecodeHintType,
  NotFoundException, // 导入NotFoundException用于错误处理
} from "@zxing/library";
import Quagga from "@ericblade/quagga2"; // 导入Quagga2用于条形码识别
import { getQrcode } from "@/api";

export default {
  data() {
    return {
      codeReader: null,
      isScanning: false,
      showSuccessEffect: false,
      lastScanTime: 0,
      isProcessing: false,
      isDestroyed: false,
      cameraIndex: null,
      devices: [],
      videoElement: null,
      scanType: "0", // 默认为巡视扫码类型
      scanInterval: null, // 用于手动扫描模式的定时器
      initRetryCount: 0, // 初始化重试计数
      maxRetryCount: 3, // 最大重试次数
      flashlightOn: false, // 闪光灯状态
      currentStream: null, // 当前视频流

      // Quagga相关状态
      quaggaInitialized: false, // Quagga是否已初始化
      isBarcodeScanActive: false, // 条形码扫描是否激活
      scanMode: "qr", // 扫描模式：'qr'(二维码), 'barcode'(条形码)，默认为二维码
      barcodeDetected: false, // 是否检测到条形码
      quaggaScanner: null, // Quagga扫描器实例
    };
  },
  onLoad(options) {
    // 获取扫码类型参数
    if (options && options.type) {
      this.scanType = options.type;
      // 减少日志输出
      // console.log("[QRCode] onLoad: 扫码类型设置为", this.scanType);
    }
  },
  mounted() {
    console.log("[QRCode] mounted");
    // 确保组件挂载时状态正确
    this.isScanning = false;
    this.isProcessing = false;
    this.isDestroyed = false;
    this.devices = [];
    this.cameraIndex = null;

    // 延迟初始化扫描器，确保DOM已完全渲染
    setTimeout(() => {
      this.initScannerWithRetry();
    }, 500);
  },

  onShow() {
    console.log("[QRCode] onShow");

    // 如果已经销毁，则重置状态
    if (this.isDestroyed) {
      this.isDestroyed = false;
      this.isScanning = false;
      this.isProcessing = false;
      this.quaggaInitialized = false;
      this.isBarcodeScanActive = false;
    }

    // 只有在未扫描、未销毁且未处理中的情况下才初始化扫描器
    if (!this.isScanning && !this.isDestroyed && !this.isProcessing) {
      // 每次显示页面时，重置设备列表和摄像头索引，确保重新获取
      this.devices = [];
      this.cameraIndex = null;

      // 延迟初始化扫描器，确保DOM已完全渲染
      setTimeout(() => {
        // 根据当前扫描模式初始化相应的扫描器
        if (this.scanMode === "barcode") {
          // 条形码模式
          this.initQuaggaScanner();
        } else {
          // 二维码模式（默认）
          this.initScannerWithRetry();
        }
      }, 500);
    }
  },
  onHide() {
    // 减少日志输出
    // console.log(
    //   "[QRCode] onHide called. Stopping scanner. isProcessing:",
    //   this.isProcessing
    // );

    // 只有在非处理状态下才停止扫描器，避免处理中断
    if (!this.isProcessing) {
      this.stopScanner();
    }
  },
  onUnload() {
    console.log("[QRCode] onUnload called");

    // 确保组件卸载时标记为已销毁
    this.isDestroyed = true;

    // 重置设备列表和摄像头索引，确保下次进入时重新获取
    this.devices = [];
    this.cameraIndex = null;

    // 清理定时器
    if (this.scanInterval) {
      clearInterval(this.scanInterval);
      this.scanInterval = null;
    }

    // 无论是否在处理中，都尝试停止扫描器和释放资源
    this.stopScanner();

    // 停止Quagga条形码扫描器
    this.stopQuaggaScanner();

    // 确保清理所有状态
    this.releaseVideoResources();

    // 强制释放内存
    if (this.codeReader) {
      this.codeReader = null;
    }

    // 重置Quagga相关状态
    this.quaggaInitialized = false;
    this.isBarcodeScanActive = false;
    this.barcodeDetected = false;
    this.quaggaScanner = null;

    // 重置状态
    this.isScanning = false;
    this.isProcessing = false;
  },

  // 页面隐藏时释放资源
  onHide() {
    console.log("[QRCode] onHide called");

    // 停止扫描器
    this.stopScanner();

    // 停止Quagga条形码扫描器
    this.stopQuaggaScanner();

    // 释放视频资源
    this.releaseVideoResources();
  },
  methods: {
    getVideoElement() {
      try {
        if (this.$refs.video && this.$refs.video.nodeType === 1) {
          // 减少日志输出
          // console.log("直接获取video元素成功");
          return this.$refs.video;
        }
        if (this.$refs.video && this.$refs.video.$el) {
          const videoEl = this.$refs.video.$el.querySelector("video");
          if (videoEl) {
            // 减少日志输出
            // console.log("通过$el获取video元素成功");
            return videoEl;
          }
          if (
            this.$refs.video.$el.childNodes &&
            this.$refs.video.$el.childNodes[0]
          ) {
            if (this.$refs.video.$el.childNodes[0].tagName === "VIDEO") {
              // 减少日志输出
              // console.log("通过childNodes[0]获取video元素成功");
              return this.$refs.video.$el.childNodes[0];
            }
            if (
              this.$refs.video.$el.childNodes[0].childNodes &&
              this.$refs.video.$el.childNodes[0].childNodes[0]
            ) {
              // 减少日志输出
              // console.log("通过childNodes[0].childNodes[0]获取video元素成功");
              return this.$refs.video.$el.childNodes[0].childNodes[0];
            }
          }
        }
        const videoByQuery = document.querySelector(".scan-video");
        if (videoByQuery) {
          // 减少日志输出
          // console.log("通过选择器获取video元素成功");
          return videoByQuery;
        }
        console.error("无法获取视频元素");
        return null;
      } catch (err) {
        console.error("获取视频元素失败:", err);
        return null;
      }
    },

    // 初始化Quagga条形码扫描器
    async initQuaggaScanner() {
      console.log("[QRCode] initQuaggaScanner called");

      if (this.quaggaInitialized || this.isDestroyed || this.isProcessing) {
        console.log(
          "[QRCode] Quagga已初始化或组件已销毁或正在处理中，取消初始化"
        );
        return false;
      }

      try {
        // 获取视频元素
        this.videoElement = this.getVideoElement();
        if (!this.videoElement) {
          console.error("[QRCode] 无法获取视频元素");
          throw new Error("无法获取视频元素");
        }

        // 确保之前的资源已释放
        this.stopQuaggaScanner();

        // 获取当前选择的摄像头设备
        let selectedDevice = null;
        if (
          this.devices &&
          this.devices.length > 0 &&
          this.cameraIndex !== null
        ) {
          selectedDevice = this.devices[this.cameraIndex];
        } else {
          // 如果没有设备列表，先获取设备列表
          const backCamera = await this.getBackCamera();
          if (backCamera) {
            selectedDevice = backCamera;
          }
        }

        if (!selectedDevice) {
          console.error("[QRCode] 无法获取摄像头设备");
          throw new Error("无法获取摄像头设备");
        }

        console.log("[QRCode] 使用摄像头初始化Quagga:", selectedDevice.label);

        // 确保之前的资源已完全释放
        this.releaseVideoResources();

        // 等待一小段时间确保资源释放
        await new Promise((resolve) => setTimeout(resolve, 300));

        // 使用MediaStream API直接获取视频流，避免Quagga内部的递归问题
        try {
          console.log("[QRCode] 尝试获取视频流，设备:", selectedDevice.label);

          // 先获取视频流，使用更宽松的约束
          const stream = await navigator.mediaDevices.getUserMedia({
            video: {
              deviceId: selectedDevice.deviceId, // 不使用exact约束，更宽松
              width: { ideal: 1280 },
              height: { ideal: 720 },
              facingMode: "environment",
            },
            audio: false,
          });

          // 将流直接分配给视频元素
          this.videoElement.srcObject = stream;
          this.currentStream = stream;

          console.log("[QRCode] 成功获取视频流并分配给视频元素");

          // 等待视频元素加载
          await new Promise((resolve) => {
            this.videoElement.onloadedmetadata = () => {
              this.videoElement.play().then(resolve);
            };
          });

          // 配置Quagga，但使用ImageCapture API而不是直接使用视频流
          const config = {
            inputStream: {
              name: "Live",
              type: "LiveStream",
              target: this.videoElement, // 使用已经设置好流的视频元素
              constraints: {
                width: { min: 640, ideal: 1280, max: 1920 },
                height: { min: 480, ideal: 720, max: 1080 },
              },
              area: {
                // 定义扫描区域，集中在中间部分以提高性能
                top: "25%",
                right: "10%",
                left: "10%",
                bottom: "25%",
              },
            },
            locator: {
              patchSize: "medium",
              halfSample: true, // 提高性能
            },
            numOfWorkers: 2, // 减少工作线程数量，避免性能问题
            frequency: 10, // 每秒尝试解码的次数
            decoder: {
              readers: [
                // 严格限制为只识别条形码格式，不识别二维码
                "ean_reader", // EAN-13和EAN-8
                "ean_8_reader", // 专门处理EAN-8
                "upc_reader", // UPC-A和UPC-E
                "upc_e_reader", // 专门处理UPC-E
                "code_128_reader", // Code 128
                "code_39_reader", // Code 39
                "code_93_reader", // Code 93
                "codabar_reader", // Codabar
                "i2of5_reader", // Interleaved 2 of 5
                // 不包含任何二维码格式
              ],
              multiple: false, // 只识别一个条形码
            },
            locate: true, // 必须定位条形码
            debug: false, // 生产环境关闭调试
          };

          // 初始化Quagga，使用已经设置好的视频流
          await Quagga.init(config);

          // 注册条形码检测事件
          Quagga.onDetected(this.handleBarcodeDetected);

          // 开始扫描
          Quagga.start();

          // 更新状态
          this.quaggaInitialized = true;
          this.isBarcodeScanActive = true;

          console.log("[QRCode] Quagga初始化成功");
          return true;
        } catch (streamError) {
          console.error("[QRCode] 获取视频流失败:", streamError);

          // 降级方案：使用原始Quagga初始化方式，但简化配置
          const config = {
            inputStream: {
              name: "Live",
              type: "LiveStream",
              target: this.videoElement,
              constraints: {
                deviceId: selectedDevice.deviceId,
                width: { min: 640, ideal: 1280, max: 1920 },
                height: { min: 480, ideal: 720, max: 1080 },
                facingMode: "environment",
              },
            },
            locator: {
              patchSize: "medium",
              halfSample: true,
            },
            numOfWorkers: 1, // 减少工作线程数量
            frequency: 5, // 降低扫描频率
            decoder: {
              // 严格限制为只识别条形码格式，不识别二维码
              readers: [
                "ean_reader",
                "ean_8_reader",
                "code_128_reader",
                "code_39_reader",
                "upc_reader",
                "upc_e_reader",
              ],
              multiple: false,
            },
            locate: true,
            debug: false,
          };

          // 初始化Quagga
          await Quagga.init(config);
          Quagga.onDetected(this.handleBarcodeDetected);
          Quagga.start();

          this.quaggaInitialized = true;
          this.isBarcodeScanActive = true;
          console.log("[QRCode] Quagga降级初始化成功");
          return true;
        }
      } catch (error) {
        console.error("[QRCode] Quagga初始化失败:", error);
        this.stopQuaggaScanner();

        // 显示错误提示但允许用户继续尝试
        uni.showToast({
          title: "条形码扫描器初始化失败，请重试",
          icon: "none",
          duration: 2000,
        });

        return false;
      }
    },

    // 停止Quagga扫描器
    stopQuaggaScanner() {
      console.log("[QRCode] stopQuaggaScanner called");

      if (this.quaggaInitialized) {
        try {
          Quagga.offDetected(this.handleBarcodeDetected);
          Quagga.stop();
          this.quaggaInitialized = false;
          this.isBarcodeScanActive = false;
          console.log("[QRCode] Quagga已停止");
        } catch (error) {
          console.error("[QRCode] 停止Quagga失败:", error);
        }
      }
    },

    // 检测扫描结果的码类型
    detectCodeType(code) {
      // 检测是否为条形码
      // 条形码通常只包含数字，有时会有少量字母，长度通常在8-14位之间
      const isBarcode =
        /^[0-9A-Z]{7,14}$/.test(code) ||
        /^[0-9]{8,14}$/.test(code) ||
        /^[0-9]{6,14}$/.test(code.replace(/[^0-9]/g, ""));

      // 检测是否为二维码
      // 二维码通常包含更多信息，可能包含URL、JSON、文本等
      // 通常长度较长，且包含各种字符
      const isQRCode =
        code.length > 14 ||
        code.includes("http") ||
        code.includes("www") ||
        code.includes(".com") ||
        code.includes("{") ||
        code.includes(":");

      if (isBarcode && !isQRCode) {
        return "barcode";
      } else if (isQRCode && !isBarcode) {
        return "qr";
      } else if (isBarcode && isQRCode) {
        // 如果同时满足两种条件，根据更明显的特征判断
        return code.length > 20 ? "qr" : "barcode";
      } else {
        // 无法确定类型，根据长度做保守判断
        return code.length <= 14 ? "barcode" : "qr";
      }
    },

    // 处理条形码检测结果
    handleBarcodeDetected(result) {
      console.log("[QRCode] handleBarcodeDetected:", result);

      if (!result || !result.codeResult || !result.codeResult.code) {
        return;
      }

      // 检查是否已经在处理中
      if (this.isProcessing) {
        console.log("[QRCode] 已在处理中，忽略条形码结果");
        return;
      }

      // 检查扫描间隔
      const now = Date.now();
      if (now - this.lastScanTime < 1500) {
        console.log("[QRCode] 扫描间隔太短，忽略条形码结果");
        return;
      }

      // 获取条形码内容
      const code = result.codeResult.code;
      console.log("[QRCode] 条形码内容:", code);

      // 验证扫描结果是否为条形码
      const codeType = this.detectCodeType(code);
      if (codeType !== "barcode") {
        console.log("[QRCode] 检测到非条形码结果，在条形码模式下忽略:", code);
        uni.showToast({
          title: "请扫描条形码",
          icon: "none",
          duration: 1500,
        });
        return;
      }

      // 更新最后扫描时间
      this.lastScanTime = now;

      // 处理扫描结果
      this.handleScanSuccess(code);
    },

    // 切换扫描模式
    async switchScanMode(mode) {
      console.log("[QRCode] switchScanMode:", mode);

      // 如果已经是指定模式，不做任何操作
      if (this.scanMode === mode) {
        return;
      }

      // 显示加载提示
      uni.showLoading({
        title: "切换中...",
        mask: true,
      });

      // 停止当前所有扫描
      this.stopScanner();
      this.stopQuaggaScanner();

      // 彻底释放视频资源
      this.releaseVideoResources();

      // 重置状态
      this.isScanning = false;
      this.quaggaInitialized = false;
      this.isBarcodeScanActive = false;

      // 设置新的扫描模式
      this.scanMode = mode;

      // 添加延迟确保资源完全释放
      await new Promise((resolve) => setTimeout(resolve, 500));

      try {
        // 根据模式初始化相应的扫描器
        if (mode === "barcode") {
          // 条形码模式
          const success = await this.initQuaggaScanner();
          if (!success) {
            console.log(
              "[QRCode] 条形码扫描器初始化失败，尝试使用二维码扫描器"
            );
            await this.initScannerWithRetry();
          }
        } else {
          // 二维码模式
          await this.initScannerWithRetry();
        }

        // 显示当前模式提示
        uni.showToast({
          title:
            mode === "barcode"
              ? "条形码扫描模式 (仅识别条形码)"
              : "二维码扫描模式 (仅识别二维码)",
          icon: "none",
          duration: 2000,
        });
      } catch (error) {
        console.error("[QRCode] 切换扫描模式失败:", error);
        uni.showToast({
          title: "切换失败，请重试",
          icon: "none",
          duration: 1500,
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 获取摄像头设备列表
    async getBackCamera() {
      try {
        // 先请求摄像头权限，这样可以确保获取到完整的设备信息
        try {
          // 先尝试获取任意摄像头的权限
          const tempStream = await navigator.mediaDevices.getUserMedia({
            video: true,
            audio: false,
          });

          // 获取权限后立即释放资源
          tempStream.getTracks().forEach((track) => track.stop());
          console.log("成功获取摄像头权限");
        } catch (permissionErr) {
          console.error("获取摄像头权限失败:", permissionErr);
          // 显示权限错误提示
          uni.showToast({
            title: "请允许使用摄像头权限",
            icon: "none",
            duration: 2000,
          });
          throw new Error("摄像头权限被拒绝");
        }

        // 权限获取成功后，再获取设备列表
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(
          (device) => device.kind === "videoinput"
        );

        // 检查设备列表是否为空
        if (videoDevices.length === 0) {
          console.error("未检测到任何摄像头设备");
          uni.showToast({
            title: "未检测到摄像头设备",
            icon: "none",
            duration: 2000,
          });
          throw new Error("未检测到任何摄像头");
        }

        // 检查设备标签是否为空（可能表示权限问题）
        const hasLabels = videoDevices.some(
          (device) => device.label && device.label.length > 0
        );
        if (!hasLabels) {
          console.warn("摄像头设备列表中没有标签信息，可能是权限问题");
        }

        this.devices = videoDevices; // 保存设备列表以便切换摄像头使用
        console.log("可用视频设备:", videoDevices);

        // 通过标签识别后置摄像头
        const backCamera = videoDevices.find((device) => {
          const label = (device.label || "").toLowerCase();

          // 华为和荣耀特有标识检测
          const isHuaweiOrHonorBack =
            (label.includes("huawei") ||
              label.includes("honor") ||
              label.includes("华为") ||
              label.includes("荣耀")) &&
            (label.includes("back") ||
              label.includes("rear") ||
              label.includes("后") ||
              label.includes("主") ||
              // 华为设备常用"camera 0"表示后置摄像头
              (label.includes("camera 0") && !label.includes("front")) ||
              // 华为和荣耀设备常见后缀数字识别：通常数字小的是后置主摄
              (/camera\s*[0-2]$/.test(label) && !label.includes("front")));

          // 原有的通用识别逻辑
          const isGeneralBack =
            (label.includes("back") ||
              label.includes("rear") ||
              label.includes("environment") ||
              label.includes("后置") ||
              label.includes("外部") ||
              label.includes("主摄") ||
              label.includes("wide") ||
              label.includes("ultra")) &&
            // 排除前置摄像头
            !(
              label.includes("front") ||
              label.includes("selfie") ||
              label.includes("user") ||
              label.includes("前置") ||
              label.includes("自拍")
            );

          return isHuaweiOrHonorBack || isGeneralBack;
        });

        if (backCamera) {
          console.log("找到后置摄像头:", backCamera.label);
          return backCamera;
        }

        // 如果标签无法识别，假设最后一个设备是后置摄像头（常见于多摄像头设备）
        if (videoDevices.length > 1) {
          const assumedBackCamera = videoDevices[videoDevices.length - 1];
          console.log(
            "未找到明确后置摄像头，使用最后一个设备:",
            assumedBackCamera.label
          );
          return assumedBackCamera;
        }

        // 如果只有一个摄像头，直接使用
        console.log("仅有一个摄像头，使用默认设备:", videoDevices[0].label);
        return videoDevices[0];
      } catch (err) {
        console.error("获取摄像头设备列表失败:", err);
        return null;
      }
    },

    // 带重试机制的初始化方法
    async initScannerWithRetry() {
      console.log(
        "[QRCode] initScannerWithRetry called, retry count:",
        this.initRetryCount
      );

      // 重置重试计数
      if (this.initRetryCount >= this.maxRetryCount) {
        console.log("[QRCode] 达到最大重试次数，显示错误提示");
        uni.showToast({
          title: "摄像头初始化失败，请退出重试",
          icon: "none",
          duration: 2000,
        });
        this.initRetryCount = 0;
        return;
      }

      try {
        await this.initScanner();
        // 如果成功初始化，重置重试计数
        this.initRetryCount = 0;
      } catch (error) {
        console.error("[QRCode] 初始化失败，准备重试:", error);
        this.initRetryCount++;

        // 释放资源后重试
        this.releaseVideoResources();
        if (this.codeReader) {
          try {
            this.codeReader.reset();
          } catch (e) {
            console.error("[QRCode] 重置codeReader失败:", e);
          }
        }

        // 延迟后重试
        setTimeout(() => {
          console.log(
            "[QRCode] 重试初始化扫描器, 第",
            this.initRetryCount,
            "次"
          );
          this.initScannerWithRetry();
        }, 1000);
      }
    },

    async initScanner() {
      console.log("[QRCode] initScanner called");

      // 检查状态
      if (this.isScanning || this.isDestroyed || this.isProcessing) {
        console.log("[QRCode] 已在扫描中或已销毁或正在处理中，取消初始化");
        return;
      }

      try {
        // 获取视频元素
        this.videoElement = this.getVideoElement();
        if (!this.videoElement) {
          console.error("[QRCode] 无法获取视频元素");
          throw new Error("无法获取视频元素");
        }

        // 确保之前的资源已释放
        this.releaseVideoResources();

        const hints = new Map();
        // 严格限制为只识别二维码格式，不识别条形码
        const formats = [
          // 只包含二维码格式
          BarcodeFormat.QR_CODE,
          BarcodeFormat.DATA_MATRIX,
          // 不包含任何条形码格式
        ];
        hints.set(DecodeHintType.POSSIBLE_FORMATS, formats);

        // 二维码识别优化参数
        hints.set(DecodeHintType.TRY_HARDER, true); // 使用更努力的解码算法
        hints.set(DecodeHintType.ALSO_INVERTED, true); // 也检测反色二维码（黑白反转）

        this.codeReader = new BrowserMultiFormatReader(hints);
        console.log("[QRCode] initScanner: BrowserMultiFormatReader created.");

        // 如果没有设备列表，先获取设备列表
        if (!this.devices || this.devices.length === 0) {
          console.log(
            "[QRCode] initScanner: No devices found, calling getBackCamera()."
          );
          const backCamera = await this.getBackCamera();
          if (backCamera) {
            // 如果成功获取到后置摄像头，设置cameraIndex为该设备在列表中的索引
            this.cameraIndex = this.devices.findIndex(
              (device) => device.deviceId === backCamera.deviceId
            );
            console.log(
              "[QRCode] initScanner: Back camera found, setting cameraIndex to:",
              this.cameraIndex
            );
          }
        }

        // 如果cameraIndex为null，则设置为后置摄像头
        if (this.cameraIndex === null) {
          console.log(
            "[QRCode] initScanner: cameraIndex is null, finding back camera."
          );
          // 查找后置摄像头
          const backCameraIndex = this.devices.findIndex((device) => {
            const label = (device.label || "").toLowerCase();

            // 华为和荣耀特有标识检测
            const isHuaweiOrHonorBack =
              (label.includes("huawei") ||
                label.includes("honor") ||
                label.includes("华为") ||
                label.includes("荣耀")) &&
              (label.includes("back") ||
                label.includes("rear") ||
                label.includes("后") ||
                label.includes("主") ||
                // 华为设备常用"camera 0"表示后置摄像头
                (label.includes("camera 0") && !label.includes("front")) ||
                // 华为和荣耀设备常见后缀数字识别：通常数字小的是后置主摄
                (/camera\s*[0-2]$/.test(label) && !label.includes("front")));

            // 通用识别逻辑
            const isGeneralBack =
              (label.includes("back") ||
                label.includes("rear") ||
                label.includes("environment") ||
                label.includes("后置") ||
                label.includes("外部") ||
                label.includes("主摄") ||
                label.includes("wide") ||
                label.includes("ultra")) &&
              !(
                label.includes("front") ||
                label.includes("selfie") ||
                label.includes("user") ||
                label.includes("前置") ||
                label.includes("自拍")
              );

            return isHuaweiOrHonorBack || isGeneralBack;
          });

          // 如果找不到明确的后置摄像头，尝试使用最后一个设备（多摄像头设备通常后置摄像头在后面）
          if (backCameraIndex === -1 && this.devices.length > 1) {
            backCameraIndex = this.devices.length - 1;
            console.log(
              "[QRCode] initScanner: No clear back camera found, using last device as back camera."
            );
          }

          this.cameraIndex = backCameraIndex !== -1 ? backCameraIndex : 0;
          console.log(
            "[QRCode] initScanner: cameraIndex set to:",
            this.cameraIndex,
            "device label:",
            this.devices[this.cameraIndex]?.label || "unknown"
          );
        }

        // 获取当前选择的摄像头设备
        const selectedDevice = this.devices[this.cameraIndex];
        if (!selectedDevice) {
          console.error(
            "[QRCode] initScanner: No selected device for cameraIndex:",
            this.cameraIndex
          );
          throw new Error("无效的摄像头索引");
        }

        console.log(
          "[QRCode] initScanner: Attempting to use camera:",
          selectedDevice.label,
          "Index:",
          this.cameraIndex
        );

        // 条形码需要更高的分辨率和更好的对焦
        const constraints = {
          video: {
            deviceId: { exact: selectedDevice.deviceId },
            width: { ideal: 1280 }, // 提高分辨率以便更好地识别条形码
            height: { ideal: 720 }, // 提高分辨率以便更好地识别条形码
            focusMode: "continuous", // 连续对焦
            exposureMode: "continuous", // 连续曝光
            // 添加更多摄像头控制参数以提高条形码识别率
            advanced: [
              {
                zoom: 1.0, // 不进行缩放，保持原始清晰度
                brightness: { ideal: 2 }, // 稍微提高亮度
                contrast: { ideal: 2 }, // 稍微提高对比度
                sharpness: { ideal: 2 }, // 提高锐度
                focusDistance: { ideal: 0.5 }, // 中等焦距，适合大多数扫码场景
              },
            ],
          },
        };

        // 使用选择的摄像头初始化扫码
        console.log(
          "[QRCode] initScanner: Calling decodeFromVideoDevice for primary strategy."
        );
        const stream = await this.codeReader.decodeFromVideoDevice(
          selectedDevice.deviceId,
          this.videoElement,
          (result, error) => {
            // 处理扫描结果
            if (result?.text) {
              console.log("扫码结果:", result);
              const now = Date.now();
              if (now - this.lastScanTime < 1500) {
                console.log(
                  "[QRCode] decodeFromVideoDevice (primary): Scan too soon, ignoring. LastScan:",
                  this.lastScanTime,
                  "Now:",
                  now
                );
                return;
              }

              // 验证扫描结果是否为二维码
              const code = result.text;
              const codeType = this.detectCodeType(code);
              if (codeType !== "qr") {
                console.log(
                  "[QRCode] 检测到非二维码结果，在二维码模式下忽略:",
                  code
                );
                uni.showToast({
                  title: "请扫描二维码",
                  icon: "none",
                  duration: 1500,
                });
                return;
              }

              this.lastScanTime = now;
              console.log(
                "[QRCode] decodeFromVideoDevice (primary): Sufficient time passed, calling handleScanSuccess. LastScanTime updated to:",
                now
              );
              this.handleScanSuccess(result.text);
            }
            // 处理错误（可选）
            else if (error && !(error instanceof NotFoundException)) {
              // 只记录非"未找到"的错误，因为"未找到"是正常的
              console.error("[QRCode] 扫描错误:", error);
            }
          },
          constraints
        );

        // 保存当前流以便控制闪光灯
        if (stream) {
          this.currentStream = stream;
        }
        this.isScanning = true;
        console.log(
          "[QRCode] initScanner: Primary scan initialization successful. Camera:",
          selectedDevice.label,
          "Index:",
          this.cameraIndex,
          "isScanning:",
          this.isScanning
        );
      } catch (err) {
        console.error(
          "[QRCode] initScanner: Primary camera initialization FAILED:",
          err
        );

        // 降级策略：尝试使用 facingMode
        try {
          console.log(
            "[QRCode] initScanner: Attempting fallback strategy with facingMode."
          );

          // 查找前置摄像头索引
          const frontCameraIndex = this.devices.findIndex((device) => {
            const label = (device.label || "").toLowerCase();

            // 增加华为和荣耀前置摄像头识别
            const isHuaweiOrHonorFront =
              (label.includes("huawei") ||
                label.includes("honor") ||
                label.includes("华为") ||
                label.includes("荣耀")) &&
              (label.includes("front") ||
                label.includes("selfie") ||
                label.includes("前") ||
                // 华为设备通常用"camera 1"表示前置摄像头
                (label.includes("camera 1") &&
                  !label.includes("back") &&
                  !label.includes("rear")));

            return (
              label.includes("front") ||
              label.includes("selfie") ||
              label.includes("user") ||
              label.includes("前置") ||
              label.includes("自拍") ||
              isHuaweiOrHonorFront
            );
          });

          // 如果当前索引是前置摄像头索引，使用user模式，否则使用environment模式
          const facingMode =
            this.cameraIndex === frontCameraIndex ? "user" : "environment";

          console.log(
            `[QRCode] initScanner: Attempting fallback with facingMode: '${facingMode}'`
          );

          // 先尝试使用exact约束
          try {
            const exactConstraints = {
              video: {
                facingMode: { exact: facingMode },
                width: { ideal: 640 }, // 降低分辨率以提高性能
                height: { ideal: 480 }, // 降低分辨率以提高性能
              },
            };

            await this.codeReader.decodeFromVideoDevice(
              undefined,
              this.videoElement,
              (result) => {
                if (result?.text) {
                  console.log("降级模式扫码结果(exact):", result);
                  const now = Date.now();
                  if (now - this.lastScanTime < 1500) return;

                  // 验证扫描结果是否为二维码
                  const code = result.text;
                  const codeType = this.detectCodeType(code);
                  if (codeType !== "qr") {
                    console.log(
                      "[QRCode] 降级模式检测到非二维码结果，在二维码模式下忽略:",
                      code
                    );
                    uni.showToast({
                      title: "请扫描二维码",
                      icon: "none",
                      duration: 1500,
                    });
                    return;
                  }

                  this.lastScanTime = now;
                  this.handleScanSuccess(result.text);
                }
              },
              exactConstraints
            );

            this.isScanning = true;
            console.log(
              `[QRCode] initScanner: Fallback scan successful with exact facingMode: '${facingMode}'`
            );
          } catch (exactErr) {
            console.log(
              `[QRCode] initScanner: Exact facingMode failed, trying preferred facingMode:`,
              exactErr
            );

            // 如果exact失败，尝试使用preferred约束
            const preferredConstraints = {
              video: {
                facingMode: facingMode, // 不使用exact，只使用preferred
                width: { ideal: 640 }, // 降低分辨率以提高性能
                height: { ideal: 480 }, // 降低分辨率以提高性能
              },
            };

            await this.codeReader.decodeFromVideoDevice(
              undefined,
              this.videoElement,
              (result) => {
                if (result?.text) {
                  console.log("降级模式扫码结果(preferred):", result);
                  const now = Date.now();
                  if (now - this.lastScanTime < 1500) return;

                  // 验证扫描结果是否为二维码
                  const code = result.text;
                  const codeType = this.detectCodeType(code);
                  if (codeType !== "qr") {
                    console.log(
                      "[QRCode] 降级模式(preferred)检测到非二维码结果，在二维码模式下忽略:",
                      code
                    );
                    uni.showToast({
                      title: "请扫描二维码",
                      icon: "none",
                      duration: 1500,
                    });
                    return;
                  }

                  this.lastScanTime = now;
                  this.handleScanSuccess(result.text);
                }
              },
              preferredConstraints
            );

            this.isScanning = true;
            console.log(
              `[QRCode] initScanner: Fallback scan successful with preferred facingMode: '${facingMode}'`
            );
          }
        } catch (fallbackErr) {
          console.error(
            "[QRCode] initScanner: All fallback strategies FAILED:",
            fallbackErr
          );

          // 确定最终降级模式的facingMode
          const finalFacingMode =
            this.cameraIndex === -1
              ? "environment"
              : this.devices.findIndex((d) => {
                  const label = (d.label || "").toLowerCase();
                  return (
                    label.includes("front") ||
                    label.includes("selfie") ||
                    label.includes("user") ||
                    label.includes("前置") ||
                    label.includes("自拍")
                  );
                }) === this.cameraIndex
              ? "user"
              : "environment";

          // 最终降级到直接访问
          this.tryDirectMediaAccess(finalFacingMode);
        }
      }
    },

    async tryDirectMediaAccess(facingMode = "environment") {
      console.log(
        `[QRCode] tryDirectMediaAccess: 尝试直接使用MediaDevices API，facingMode: ${facingMode}`
      );
      try {
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          throw new Error("浏览器不支持MediaDevices API");
        }
        if (!this.videoElement) {
          this.videoElement = this.getVideoElement();
          if (!this.videoElement) {
            throw new Error("无法获取视频元素");
          }
        }

        // 先尝试使用exact约束
        try {
          console.log(
            `[QRCode] tryDirectMediaAccess: 尝试使用exact约束: ${facingMode}`
          );
          const stream = await navigator.mediaDevices.getUserMedia({
            video: {
              facingMode: { exact: facingMode },
              width: { ideal: 640 }, // 降低分辨率以提高性能
              height: { ideal: 480 }, // 降低分辨率以提高性能
            },
          });
          this.videoElement.srcObject = stream;
          this.currentStream = stream; // 保存当前流以便控制闪光灯
          await this.videoElement.play();
          this.isScanning = true;
          console.log(
            `[QRCode] tryDirectMediaAccess: 成功使用exact约束打开${
              facingMode === "user" ? "前置" : "后置"
            }摄像头`
          );
        } catch (exactErr) {
          console.log(
            `[QRCode] tryDirectMediaAccess: exact约束失败，尝试使用preferred约束:`,
            exactErr
          );

          // 如果exact失败，尝试使用preferred约束
          const stream = await navigator.mediaDevices.getUserMedia({
            video: {
              facingMode: facingMode, // 不使用exact，只使用preferred
              width: { ideal: 640 }, // 降低分辨率以提高性能
              height: { ideal: 480 }, // 降低分辨率以提高性能
            },
          });
          this.videoElement.srcObject = stream;
          this.currentStream = stream; // 保存当前流以便控制闪光灯
          await this.videoElement.play();
          this.isScanning = true;
          console.log(
            `[QRCode] tryDirectMediaAccess: 成功使用preferred约束打开${
              facingMode === "user" ? "前置" : "后置"
            }摄像头`
          );
        }

        // 显示提示
        uni.showToast({
          title: "扫码功能受限，请对准二维码",
          icon: "none",
          duration: 2000,
        });

        // 设置一个定时器，每隔一段时间尝试从视频帧中解码
        // 减少间隔时间以提高识别率，但不要太短以避免性能问题
        this.scanInterval = setInterval(() => {
          if (!this.isScanning || this.isDestroyed || this.isProcessing) {
            clearInterval(this.scanInterval);
            return;
          }

          try {
            if (this.videoElement && this.videoElement.videoWidth > 0) {
              const canvas = this.$refs.canvas;
              const context = canvas.getContext("2d");

              // 条形码需要更高的分辨率
              const scaleFactor = 1.0; // 使用原始分辨率，不缩放
              const width = this.videoElement.videoWidth * scaleFactor;
              const height = this.videoElement.videoHeight * scaleFactor;

              canvas.width = width;
              canvas.height = height;

              // 绘制原始图像
              context.drawImage(this.videoElement, 0, 0, width, height);

              // 条形码图像增强处理
              try {
                const imageData = context.getImageData(0, 0, width, height);
                const data = imageData.data;

                // 条形码特化的图像处理
                for (let i = 0; i < data.length; i += 4) {
                  // 计算灰度值 - 使用加权平均法，更符合人眼感知
                  const gray =
                    0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];

                  // 二值化处理 - 对条形码特别有效
                  const threshold = 128;
                  const bw = gray > threshold ? 255 : 0;

                  // 增强黑白对比 - 条形码识别的关键
                  data[i] = bw;
                  data[i + 1] = bw;
                  data[i + 2] = bw;
                }

                // 应用处理后的图像
                context.putImageData(imageData, 0, 0);

                // 额外处理：锐化边缘，对条形码特别有效
                context.globalCompositeOperation = "overlay";
                context.drawImage(canvas, 0, 0, width, height);
                context.globalCompositeOperation = "source-over";
              } catch (enhanceErr) {
                // 如果增强失败，继续使用原始图像
                console.error("[QRCode] 条形码图像增强失败:", enhanceErr);
              }

              // 获取处理后的图像数据
              const finalImageData = context.getImageData(0, 0, width, height);

              if (this.codeReader) {
                this.codeReader
                  .decodeFromImageData(finalImageData)
                  .then((result) => {
                    if (result?.text) {
                      const now = Date.now();
                      if (now - this.lastScanTime < 1500) return;

                      // 验证扫描结果是否为二维码
                      const code = result.text;
                      const codeType = this.detectCodeType(code);
                      if (codeType !== "qr" && this.scanMode === "qr") {
                        console.log(
                          "[QRCode] 手动模式检测到非二维码结果，在二维码模式下忽略:",
                          code
                        );
                        uni.showToast({
                          title: "请扫描二维码",
                          icon: "none",
                          duration: 1500,
                        });
                        return;
                      }

                      this.lastScanTime = now;
                      clearInterval(this.scanInterval);
                      this.handleScanSuccess(result.text);
                    }
                  })
                  .catch(() => {
                    // 解码失败，继续下一帧
                  });
              }
            }
          } catch (e) {
            console.error("[QRCode] 手动解码失败:", e);
          }
        }, 500); // 减少到500ms，提高扫描频率
      } catch (err) {
        console.error(
          `[QRCode] tryDirectMediaAccess: 使用facingMode=${facingMode}初始化失败:`,
          err
        );

        // 如果指定的facingMode失败，尝试另一种模式
        if (facingMode === "user") {
          try {
            await this.tryDirectMediaAccess("environment");
            return;
          } catch (finalErr) {
            console.error(
              "[QRCode] tryDirectMediaAccess: 所有相机初始化方式均失败"
            );
          }
        } else if (facingMode === "environment") {
          try {
            await this.tryDirectMediaAccess("user");
            return;
          } catch (finalErr) {
            console.error(
              "[QRCode] tryDirectMediaAccess: 所有相机初始化方式均失败"
            );
          }
        }

        uni.showToast({
          title: "相机无法打开，请检查权限",
          icon: "error",
          duration: 2000,
        });
      }
    },

    toggleCamera() {
      console.log("[QRCode] toggleCamera called");

      // 检查设备列表
      if (!this.devices || this.devices.length === 0) {
        console.log("[QRCode] 没有可用摄像头设备，尝试重新获取");
        uni.showToast({
          title: "正在检测摄像头...",
          icon: "none",
          duration: 1500,
        });

        // 尝试重新初始化
        this.initScannerWithRetry();
        return;
      }

      // 如果只有一个摄像头，提示用户
      if (this.devices.length <= 1) {
        console.log("[QRCode] 只有一个摄像头，无法切换");
        uni.showToast({
          title: "没有可用的其他摄像头",
          icon: "none",
          duration: 1500,
        });
        return;
      }

      // 停止当前扫描
      this.stopScanner();
      console.log("[QRCode] 已停止扫描器，准备切换摄像头");

      // 直接在前后摄像头之间切换，而不是循环所有摄像头
      // 查找前置摄像头
      const frontCamera = this.devices.findIndex((device) => {
        const label = (device.label || "").toLowerCase();

        // 增加华为和荣耀前置摄像头识别
        const isHuaweiOrHonorFront =
          (label.includes("huawei") ||
            label.includes("honor") ||
            label.includes("华为") ||
            label.includes("荣耀")) &&
          (label.includes("front") ||
            label.includes("selfie") ||
            label.includes("前") ||
            // 华为设备通常用"camera 1"表示前置摄像头
            (label.includes("camera 1") &&
              !label.includes("back") &&
              !label.includes("rear")));

        return (
          label.includes("front") ||
          label.includes("selfie") ||
          label.includes("user") ||
          label.includes("前置") ||
          label.includes("自拍") ||
          isHuaweiOrHonorFront
        );
      });
      console.log("[QRCode] toggleCamera: frontCamera index:", frontCamera);

      // 查找后置摄像头
      const backCamera = this.devices.findIndex((device) => {
        const label = (device.label || "").toLowerCase();

        // 增加华为和荣耀后置摄像头识别
        const isHuaweiOrHonorBack =
          (label.includes("huawei") ||
            label.includes("honor") ||
            label.includes("华为") ||
            label.includes("荣耀")) &&
          (label.includes("back") ||
            label.includes("rear") ||
            label.includes("后") ||
            label.includes("主") ||
            // 华为设备常用"camera 0"表示后置摄像头
            (label.includes("camera 0") && !label.includes("front")) ||
            // 华为和荣耀设备常见后缀数字识别：通常数字小的是后置主摄
            (/camera\s*[0-2]$/.test(label) && !label.includes("front")));

        return (
          (label.includes("back") ||
            label.includes("rear") ||
            label.includes("environment") ||
            label.includes("后置") ||
            label.includes("外部") ||
            label.includes("主摄") ||
            label.includes("wide") ||
            label.includes("ultra") ||
            isHuaweiOrHonorBack) &&
          !(
            label.includes("front") ||
            label.includes("selfie") ||
            label.includes("user") ||
            label.includes("前置") ||
            label.includes("自拍")
          )
        );
      });
      console.log("[QRCode] toggleCamera: backCamera index:", backCamera);

      // 改进摄像头切换逻辑
      const oldCameraIndex = this.cameraIndex;

      // 检查当前摄像头是否是前置摄像头
      const isCurrentFront =
        frontCamera !== -1 && this.cameraIndex === frontCamera;

      if (isCurrentFront) {
        // 如果当前是前置摄像头，切换到后置摄像头
        this.cameraIndex = backCamera !== -1 ? backCamera : 0;
        console.log(
          "[QRCode] toggleCamera: 从前置切换到后置摄像头:",
          this.devices[this.cameraIndex].label
        );
        uni.showToast({
          title: "已切换到后置摄像头",
          icon: "none",
          duration: 1500,
        });
      } else {
        // 如果当前是后置摄像头或其他，切换到前置摄像头
        if (frontCamera !== -1) {
          this.cameraIndex = frontCamera;
          console.log(
            "[QRCode] toggleCamera: 从后置切换到前置摄像头:",
            this.devices[frontCamera].label
          );
          uni.showToast({
            title: "已切换到前置摄像头",
            icon: "none",
            duration: 1500,
          });
        } else {
          // 如果没有找到前置摄像头，则在可用摄像头中循环
          this.cameraIndex = (this.cameraIndex + 1) % this.devices.length;
          console.log(
            "[QRCode] toggleCamera: 没有找到前置摄像头，循环切换到下一个摄像头:",
            this.devices[this.cameraIndex].label
          );
          uni.showToast({
            title: "已切换摄像头",
            icon: "none",
            duration: 1500,
          });
        }
      }

      console.log(
        `[QRCode] toggleCamera: Camera switched from index ${oldCameraIndex} to ${this.cameraIndex}`
      );

      // 重新初始化扫描器，使用带重试机制的方法
      setTimeout(() => {
        console.log("[QRCode] 重新初始化扫描器，使用新的摄像头");
        this.initScannerWithRetry();
      }, 500);
    },

    // 释放视频资源的专用方法
    releaseVideoResources() {
      console.log("[QRCode] releaseVideoResources called");

      // 关闭闪光灯
      if (this.flashlightOn) {
        try {
          const videoTrack = this.videoElement?.srcObject?.getVideoTracks()[0];
          if (
            videoTrack &&
            videoTrack.getCapabilities &&
            videoTrack.getCapabilities().torch
          ) {
            videoTrack.applyConstraints({
              advanced: [{ torch: false }],
            });
            this.flashlightOn = false;
            console.log("[QRCode] 闪光灯已关闭");
          }
        } catch (err) {
          console.error("[QRCode] 关闭闪光灯失败:", err);
        }
      }

      // 释放视频资源
      if (this.videoElement && this.videoElement.srcObject) {
        try {
          const tracks = this.videoElement.srcObject.getTracks();
          tracks.forEach((track) => {
            try {
              track.stop();
              console.log(`[QRCode] 成功停止视频轨道: ${track.kind}`);
            } catch (err) {
              console.error("[QRCode] 停止视频轨道失败:", err);
            }
          });
          this.videoElement.srcObject = null;
          console.log("[QRCode] 视频元素srcObject已清空");
        } catch (err) {
          console.error("[QRCode] 释放视频资源失败:", err);
        }
      }

      // 释放当前流
      if (this.currentStream) {
        try {
          const tracks = this.currentStream.getTracks();
          tracks.forEach((track) => {
            try {
              track.stop();
              console.log(`[QRCode] 成功停止当前流轨道: ${track.kind}`);
            } catch (err) {
              console.error("[QRCode] 停止当前流轨道失败:", err);
            }
          });
          this.currentStream = null;
          console.log("[QRCode] 当前流已释放");
        } catch (err) {
          console.error("[QRCode] 释放当前流失败:", err);
        }
      }

      // 尝试释放所有可能存在的媒体流
      try {
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
          // 创建一个空的媒体流并立即关闭，这有助于释放系统摄像头资源
          navigator.mediaDevices
            .getUserMedia({ audio: false, video: false })
            .then((stream) => {
              stream.getTracks().forEach((track) => track.stop());
            })
            .catch(() => {
              // 忽略错误
            });
        }
      } catch (err) {
        // 忽略错误
      }

      // 重置视频元素
      if (this.videoElement) {
        try {
          this.videoElement.pause();
          this.videoElement.removeAttribute("src");
          this.videoElement.load();
        } catch (err) {
          console.error("[QRCode] 重置视频元素失败:", err);
        }
      }

      console.log("[QRCode] 视频资源已完全释放");
    },

    stopScanner() {
      console.log("[QRCode] stopScanner called");

      // 清理定时器
      if (this.scanInterval) {
        clearInterval(this.scanInterval);
        this.scanInterval = null;
        console.log("[QRCode] 扫描定时器已清理");
      }

      // 停止codeReader
      if (this.codeReader) {
        try {
          this.codeReader.reset();
          console.log("[QRCode] codeReader已重置");
        } catch (err) {
          console.error("[QRCode] 重置codeReader失败:", err);
        }
      }

      // 停止Quagga条形码扫描器
      this.stopQuaggaScanner();

      // 释放视频资源
      this.releaseVideoResources();

      // 重置扫描状态
      this.isScanning = false;
      this.quaggaInitialized = false;
      this.isBarcodeScanActive = false;

      // 添加短暂延迟，确保资源完全释放
      setTimeout(() => {
        console.log("[QRCode] 扫描器已完全停止");
      }, 100);
    },

    // 控制闪光灯
    async toggleFlashlight() {
      console.log(
        "[QRCode] toggleFlashlight called, current state:",
        this.flashlightOn
      );

      try {
        // 检查是否有当前视频流
        if (!this.videoElement || !this.videoElement.srcObject) {
          console.log("[QRCode] 没有可用的视频流，无法控制闪光灯");
          uni.showToast({
            title: "无法控制闪光灯，请确保相机已打开",
            icon: "none",
            duration: 2000,
          });
          return;
        }

        // 获取视频轨道
        const videoTrack = this.videoElement.srcObject.getVideoTracks()[0];
        if (!videoTrack) {
          console.log("[QRCode] 未找到视频轨道");
          uni.showToast({
            title: "未找到视频轨道，无法控制闪光灯",
            icon: "none",
            duration: 2000,
          });
          return;
        }

        // 检查是否支持闪光灯功能
        const capabilities = videoTrack.getCapabilities
          ? videoTrack.getCapabilities()
          : {};
        if (!capabilities.torch) {
          console.log("[QRCode] 当前设备不支持闪光灯功能");
          uni.showToast({
            title: "当前设备不支持闪光灯功能",
            icon: "none",
            duration: 2000,
          });
          return;
        }

        // 切换闪光灯状态
        const newState = !this.flashlightOn;
        await videoTrack.applyConstraints({
          advanced: [{ torch: newState }],
        });

        // 更新状态
        this.flashlightOn = newState;
        console.log(
          "[QRCode] 闪光灯状态已切换为:",
          this.flashlightOn ? "开启" : "关闭"
        );

        // 显示提示
        uni.showToast({
          title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
          icon: "none",
          duration: 1500,
        });
      } catch (error) {
        console.error("[QRCode] 控制闪光灯失败:", error);
        uni.showToast({
          title: "控制闪光灯失败，可能不支持此功能",
          icon: "none",
          duration: 2000,
        });
      }
    },

    async handleScanSuccess(result) {
      console.log(
        "[QRCode] handleScanSuccess called with result:",
        result,
        ". Current isProcessing:",
        this.isProcessing
      );
      if (this.isProcessing) {
        console.log(
          "[QRCode] handleScanSuccess: Aborting, already processing."
        );
        return;
      }
      try {
        // 设置处理中状态，防止重复调用
        this.isProcessing = true;
        console.log(
          "[QRCode] handleScanSuccess: Set isProcessing to true. Stopping scanner."
        );
        this.stopScanner(); // 立即停止扫描
        console.log(
          "[QRCode] handleScanSuccess: Scanner stop requested. Calling getQrcode API."
        );

        // 调用API前先设置isDestroyed，防止onShow重新初始化
        this.isDestroyed = true;

        // 根据扫码类型传递不同的参数
        const params = {
          codeId: result,
          type: this.scanType, // 添加type参数
        };
        console.log(
          "[QRCode] handleScanSuccess: 调用getQrcode API，参数:",
          params
        );

        const response = await getQrcode(params);
        console.log(
          "[QRCode] handleScanSuccess: getQrcode API success. Response:",
          response
        );
        uni.vibrateShort();
        this.showSuccessEffect = true;
        console.log(
          "[QRCode] handleScanSuccess: Success effect shown. Scheduling redirect."
        );
        setTimeout(() => {
          console.log(
            "[QRCode] handleScanSuccess: setTimeout callback. Hiding success effect, redirecting."
          );
          this.showSuccessEffect = false;
          const encodedData = encodeURIComponent(JSON.stringify(response));

          // 根据扫码类型决定跳转页面
          let targetUrl = "";
          if (this.scanType === "1") {
            // 走访扫码，跳转到no-form页面
            targetUrl = `/pages/no-form/index?scanData=${encodedData}`;
          } else {
            // 巡视扫码，跳转到order页面
            targetUrl = `/pages/order/index?scanData=${encodedData}`;
          }

          // 使用reLaunch而不是redirectTo，确保完全销毁当前页面
          uni.reLaunch({
            url: targetUrl,
            success: () => {
              console.log(
                `[QRCode] Navigation to ${
                  this.scanType === "1" ? "no-form" : "order"
                } page successful`
              );
            },
            fail: (err) => {
              console.error("[QRCode] Navigation failed:", err);
              // 导航失败时，重置状态
              this.isProcessing = false;
              this.isDestroyed = false;
              uni.showToast({
                title: "页面跳转失败",
                icon: "error",
                duration: 2000,
              });
            },
          });
          console.log("[QRCode] handleScanSuccess: Redirect initiated.");
        }, 800);
      } catch (error) {
        console.error(
          "[QRCode] handleScanSuccess: getQrcode API FAILED. Error:",
          error
        );
        uni.showToast({
          title: error?.response?.msg || "扫码失败",
          icon: "error",
          duration: 2000,
        });
        console.log(
          "[QRCode] handleScanSuccess: Error toast shown. Scheduling reset of isProcessing."
        );

        // 在API调用失败后，设置一个标志防止重复初始化
        this.isDestroyed = true;

        setTimeout(() => {
          console.log(
            "[QRCode] handleScanSuccess (error): setTimeout callback. Resetting isProcessing."
          );
          this.isProcessing = false;

          // 在API失败后，返回上一页或首页，避免留在扫码页面
          uni.navigateBack({
            delta: 1,
            fail: () => {
              // 如果无法返回上一页，则跳转到首页
              uni.reLaunch({
                url: "/pages/index",
              });
            },
          });
        }, 2000);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  overflow: hidden;
}

.scan-box {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  pointer-events: none;
  -webkit-transform: none !important;
  transform: none !important;
}

::v-deep .uni-video-video {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

::v-deep .uni-video-cover,
.uni-video-control {
  display: none !important;
}

.scan-canvas {
  display: none;
}

.scan-line {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 240px;
  height: 240px;
  transform: translate(-50%, -50%);
  border: 1px solid #333;
  box-shadow: 0 0 0 4000px rgba(0, 0, 0, 0.6);
}

.scan-border {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 240px;
  height: 240px;
  transform: translate(-50%, -50%);
  border: 5px solid #138071;
  animation: scanBorder 2s linear infinite;
}

.tip-text {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 100px;
  color: #ffffff;
  text-align: center;
  font-size: 16px;

  .barcode-tip {
    margin-top: 10px;
    font-size: 14px;
    color: #ffcc00; // 黄色提示更醒目
    font-weight: bold;
    animation: pulse 2s infinite; // 添加脉冲动画提高注意力
  }

  .qrcode-tip {
    margin-top: 10px;
    font-size: 14px;
    color: #00ccff; // 蓝色提示区分二维码模式
    font-weight: bold;
    animation: pulse 2s infinite;
  }

  .mode-restriction {
    display: inline-block;
    margin-top: 5px;
    padding: 3px 8px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.success-effect {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
  z-index: 999;
}

.success-icon {
  width: 100px;
  height: 100px;
  background: #138071;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: scaleIn 0.3s ease;
}

.success-text {
  color: #fff;
  font-size: 18px;
  margin-top: 20px;
}

@keyframes scanBorder {
  0% {
    clip-path: inset(0 98% 98% 0);
  }
  25% {
    clip-path: inset(0 0 98% 0);
  }
  50% {
    clip-path: inset(0 0 0 98%);
  }
  75% {
    clip-path: inset(98% 0 0 0);
  }
  100% {
    clip-path: inset(0 98% 98% 0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.5);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

:deep(.uni-video-video) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

::v-deep .uni-video-cover,
::v-deep .uni-video-control,
::v-deep .uni-video-poster,
::v-deep .uni-video-button,
::v-deep .uni-video-fullscreen,
::v-deep .uni-video-center-button,
::v-deep .uni-video-seekbar,
::v-deep .uni-video-seekbar-wrap,
::v-deep .uni-video-seekbar-thumb,
::v-deep .uni-video-seekbar-progress,
::v-deep .uni-video-seekbar-buffer,
::v-deep .uni-video-seekbar-played {
  display: none !important;
}

.camera-toggle-btn {
  position: absolute;
  z-index: 1000;
  top: 30px;
  right: 20px;
  padding: 10px 15px;
  background-color: rgba(255, 255, 255, 0.3);
  color: #fff;
  border-radius: 8px;
  text-align: center;
}

.flashlight-btn {
  position: absolute;
  z-index: 1000;
  bottom: 180px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  text {
    margin-top: 5px;
    font-size: 14px;
  }
}

.scan-mode-container {
  position: absolute;
  z-index: 1000;
  bottom: 240px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15px; /* 增加按钮间距 */
  padding: 5px;
}

.scan-mode-btn {
  padding: 12px 20px; /* 增加按钮大小 */
  border-radius: 25px; /* 更圆润的边角 */
  background-color: rgba(0, 0, 0, 0.7); /* 更深的背景色 */
  color: #fff;
  font-size: 16px; /* 更大的字体 */
  text-align: center;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3); /* 添加阴影效果 */
  border: 1px solid rgba(255, 255, 255, 0.2); /* 添加边框 */

  &.active {
    background-color: #138071;
    color: #fff;
    font-weight: bold;
    transform: scale(1.05); /* 活跃状态稍微放大 */
    box-shadow: 0 4px 12px rgba(19, 128, 113, 0.5); /* 活跃状态阴影更明显 */
  }

  text {
    font-size: 16px;
  }
}
</style>
